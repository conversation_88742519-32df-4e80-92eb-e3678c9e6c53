# Database Provider Setup

This document describes the database provider implementation for the Homeo API application.

## Overview

The database provider follows Clean Architecture principles by:
- Keeping database concerns in the infrastructure layer
- Using dependency injection for loose coupling
- Providing a clean interface for database operations
- Supporting both development and production environments

## Architecture

```
packages/db/                    # Database package
├── src/
│   ├── client.ts              # Database client with connection pooling
│   ├── index.ts               # Main exports
│   └── schema/                # Drizzle schema definitions
│       ├── index.ts           # Schema exports
│       ├── listings.ts        # Listings table
│       ├── properties.ts      # Properties table
│       ├── locations.ts       # Locations table
│       └── ...                # Other schema files

apps/api/src/infrastructure/
├── providers/
│   ├── database.provider.ts   # NestJS database provider
│   └── index.ts               # Provider exports
└── repositories/
    ├── drizzle-listing.repository.ts  # Drizzle-based repository
    └── in-memory-listing.repository.ts # In-memory repository
```

## Components

### 1. Database Client (`packages/db/src/client.ts`)

- **Connection Pooling**: Uses PostgreSQL connection pool for optimal performance
- **Environment Configuration**: Reads `DATABASE_URL` from environment variables
- **Type Safety**: Fully typed with <PERSON><PERSON>zle ORM
- **Connection Management**: Provides connection testing and cleanup functions

Key features:
- Maximum 20 connections in pool
- 30-second idle timeout
- 2-second connection timeout
- Graceful shutdown support

### 2. Database Provider (`apps/api/src/infrastructure/providers/database.provider.ts`)

- **NestJS Integration**: Provides database client through dependency injection
- **Clean Architecture**: Keeps database concerns in infrastructure layer
- **Type Safety**: Exports typed Database interface

### 3. Repository Implementation (`apps/api/src/infrastructure/repositories/drizzle-listing.repository.ts`)

- **Interface Compliance**: Implements `IListingRepository` interface
- **Database Operations**: Uses Drizzle ORM for database queries
- **Error Handling**: Proper error handling and logging
- **Connection Testing**: Includes database connectivity verification

## Usage

### 1. Environment Setup

Set the `DATABASE_URL` environment variable:

```bash
# For local development
DATABASE_URL="postgresql://username:password@localhost:5432/homeo_dev"

# For production (example with Neon)
DATABASE_URL="postgresql://username:<EMAIL>/homeo_prod"
```

### 2. Dependency Injection

The database client is available through NestJS dependency injection:

```typescript
import { Inject, Injectable } from '@nestjs/common';
import { type Database } from '@repo/db';
import { INJECTION_TOKENS } from '../../shared/constants';

@Injectable()
export class MyService {
  constructor(
    @Inject(INJECTION_TOKENS.DATABASE)
    private readonly db: Database,
  ) {}

  async someMethod() {
    // Use this.db for database operations
    const results = await this.db.select().from(someTable);
    return results;
  }
}
```

### 3. Repository Pattern

Use repositories for data access:

```typescript
// In your use case or service
constructor(
  @Inject(INJECTION_TOKENS.LISTING_REPOSITORY)
  private readonly listingRepository: IListingRepository
) {}
```

## Health Checks

The health service includes database connectivity checks:

```bash
curl http://localhost:3001/api/health
```

Response includes database status:
```json
{
  "statusCode": 200,
  "data": {
    "status": "healthy",
    "uptime": 12345,
    "timestamp": "2024-01-01T00:00:00.000Z",
    "details": {
      "database": {
        "connected": true
      },
      "memory": { ... },
      "nodeVersion": "v20.0.0",
      "platform": "darwin",
      "arch": "arm64"
    }
  }
}
```

## Testing

### Connection Test

Run the connection test script:

```bash
cd apps/api
bun run test-db-connection.ts
```

### Unit Tests

Repository tests should mock the database:

```typescript
const mockDb = {
  select: jest.fn(),
  insert: jest.fn(),
  update: jest.fn(),
  delete: jest.fn(),
};

const repository = new DrizzleListingRepository(mockDb as any);
```

## Migration and Schema Management

The database package uses Drizzle Kit for schema management:

```bash
cd packages/db

# Generate migrations
bun run db:generate

# Push schema changes
bun run db:push

# Open Drizzle Studio
bun run db:studio
```

## Production Considerations

1. **Connection Pooling**: Configured for optimal performance
2. **Error Handling**: Comprehensive error handling and logging
3. **Health Monitoring**: Database connectivity monitoring
4. **Graceful Shutdown**: Proper connection cleanup on application shutdown
5. **Environment Variables**: Secure configuration management

## Next Steps

1. **Complete Repository Implementation**: Implement full CRUD operations in `DrizzleListingRepository`
2. **Add More Repositories**: Create repositories for other entities (properties, locations, etc.)
3. **Add Transactions**: Implement transaction support for complex operations
4. **Add Caching**: Consider adding Redis caching layer
5. **Add Monitoring**: Implement database performance monitoring
6. **Add Migrations**: Set up proper migration workflow for production

## Troubleshooting

### Common Issues

1. **Connection Errors**: Check `DATABASE_URL` environment variable
2. **Type Errors**: Ensure `@repo/db` package is built (`bun run build`)
3. **Import Errors**: Verify package dependencies are installed
4. **Schema Errors**: Run `bun run db:generate` after schema changes

### Debug Mode

Enable debug logging by setting:
```bash
DEBUG=drizzle:*
```
