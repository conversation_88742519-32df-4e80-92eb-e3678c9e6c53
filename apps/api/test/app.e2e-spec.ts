import { INestApplication } from '@nestjs/common';
import { FastifyAdapter } from '@nestjs/platform-fastify';
import { Test, TestingModule } from '@nestjs/testing';
import request from 'supertest';
import { AppModule } from './../src/app.module';

describe('App (e2e)', () => {
  let app: INestApplication;

  beforeEach(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [AppModule],
    }).compile();

    app = moduleFixture.createNestApplication(new FastifyAdapter() as any);
    app.setGlobalPrefix('api');
    await app.init();
    await app.getHttpAdapter().getInstance().ready();
  });

  afterEach(async () => {
    await app.close();
  });

  it('/api/health (GET)', () => {
    return request(app.getHttpServer())
      .get('/api/health')
      .expect(200)
      .expect((res) => {
        expect(res.body.statusCode).toBe(200);
        expect(res.body.data.status).toBe('healthy');
        expect(res.body.data.uptime).toBeGreaterThan(0);
      });
  });

  it('/api/listings (GET)', () => {
    return request(app.getHttpServer())
      .get('/api/listings')
      .expect(200)
      .expect((res) => {
        expect(res.body.statusCode).toBe(200);
        expect(res.body.data.data).toEqual([]);
        expect(res.body.data.total).toBe(0);
      });
  });

  it('/api/listings (POST) should create a listing', () => {
    const createListingDto = {
      title: 'Test Listing',
      description: 'A test listing',
      price: 1000,
      currency: 'USD',
      location: {
        address: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zipCode: '12345',
      },
      propertyType: 'apartment',
      bedrooms: 2,
      bathrooms: 1,
      amenities: [],
      images: [],
      ownerId: 'owner123',
      contactInfo: {
        email: '<EMAIL>',
      },
    };

    return request(app.getHttpServer())
      .post('/api/listings')
      .send(createListingDto)
      .expect(201)
      .expect((res) => {
        expect(res.body.statusCode).toBe(201);
        expect(res.body.data.title).toBe('Test Listing');
        expect(res.body.data.price).toBe(1000);
        expect(res.body.data.status).toBe('active');
      });
  });
});
