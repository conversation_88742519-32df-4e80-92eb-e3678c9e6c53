/**
 * Simple test script to verify database connection
 * Run with: bun run test-db-connection.ts
 */

import { testConnection } from '@repo/db';

async function main() {
  console.log('Testing database connection...');
  
  try {
    const isConnected = await testConnection();
    
    if (isConnected) {
      console.log('✅ Database connection successful!');
    } else {
      console.log('❌ Database connection failed');
    }
  } catch (error) {
    console.error('❌ Error testing database connection:', error);
  }
  
  process.exit(0);
}

main();
