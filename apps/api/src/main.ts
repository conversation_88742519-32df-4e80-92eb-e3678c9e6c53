import { Logger } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { FastifyAdapter } from '@nestjs/platform-fastify';
import { AppModule } from './app.module';
import { APP_CONSTANTS } from './shared/constants';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  const app = await NestFactory.create(AppModule, new FastifyAdapter() as any, {
    logger: ['error', 'warn', 'log'],
  });

  // Enable CORS
  app.enableCors({
    origin: true,
    methods: APP_CONSTANTS.CORS_METHODS,
    credentials: true,
  });

  // Global prefix for API routes
  app.setGlobalPrefix(APP_CONSTANTS.API_PREFIX);

  const port = process.env.PORT ?? APP_CONSTANTS.DEFAULT_PORT;
  await app.listen(port, '0.0.0.0');

  logger.log(
    `🚀 Application is running on: http://localhost:${port}/${APP_CONSTANTS.API_PREFIX}`
  );
}

bootstrap().catch((error) => {
  const logger = new Logger('Bootstrap');
  logger.error('❌ Error starting server:', error);
  process.exit(1);
});
