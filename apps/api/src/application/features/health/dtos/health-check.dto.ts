import { z } from 'zod';

export const HealthCheckRequestSchema = z.object({
  includeDetails: z.boolean().optional().default(false),
});

export const HealthCheckResponseSchema = z.object({
  status: z.enum(['healthy', 'unhealthy']),
  timestamp: z.date(),
  uptime: z.number(),
  details: z.record(z.string(), z.any()).optional(),
});

export type HealthCheckRequest = z.infer<typeof HealthCheckRequestSchema>;
export type HealthCheckResponse = z.infer<typeof HealthCheckResponseSchema>;
