import { Test, TestingModule } from '@nestjs/testing';
import { HealthCheckUseCase } from './health-check.use-case';
import type { IHealthService } from '../interfaces';
import { INJECTION_TOKENS } from '../../../../shared/constants';

describe('HealthCheckUseCase', () => {
  let useCase: HealthCheckUseCase;
  let healthService: jest.Mocked<IHealthService>;

  beforeEach(async () => {
    const mockHealthService: jest.Mocked<IHealthService> = {
      checkHealth: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        HealthCheckUseCase,
        {
          provide: INJECTION_TOKENS.HEALTH_SERVICE,
          useValue: mockHealthService,
        },
      ],
    }).compile();

    useCase = module.get<HealthCheckUseCase>(HealthCheckUseCase);
    healthService = module.get(INJECTION_TOKENS.HEALTH_SERVICE);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  it('should return health data successfully', async () => {
    const mockHealthData = {
      status: 'healthy' as const,
      uptime: 12345,
      timestamp: new Date(),
      details: { memory: '100MB' },
    };

    healthService.checkHealth.mockResolvedValue(mockHealthData);

    const result = await useCase.execute({ includeDetails: true });

    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data.status).toBe('healthy');
      expect(result.data.uptime).toBe(12345);
      expect(result.data.details).toEqual({ memory: '100MB' });
    }
  });

  it('should return health data without details when not requested', async () => {
    const mockHealthData = {
      status: 'healthy' as const,
      uptime: 12345,
      timestamp: new Date(),
      details: { memory: '100MB' },
    };

    healthService.checkHealth.mockResolvedValue(mockHealthData);

    const result = await useCase.execute({ includeDetails: false });

    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data.status).toBe('healthy');
      expect(result.data.uptime).toBe(12345);
      expect(result.data.details).toBeUndefined();
    }
  });

  it('should handle errors gracefully', async () => {
    healthService.checkHealth.mockRejectedValue(new Error('Service unavailable'));

    const result = await useCase.execute({ includeDetails: false });

    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.message).toBe('Service unavailable');
    }
  });
});
