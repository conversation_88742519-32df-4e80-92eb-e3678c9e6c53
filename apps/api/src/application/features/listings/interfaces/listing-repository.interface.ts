import { PaginationOptions, PaginatedResult } from '../../../../shared/types';
import { 
  CreateListingRequest, 
  CreateListingResponse,
  GetListingsRequest,
  ListingItem,
  GetListingByIdResponse
} from '../dtos';

export interface IListingRepository {
  create(data: CreateListingRequest): Promise<CreateListingResponse>;
  findById(id: string): Promise<GetListingByIdResponse | null>;
  findMany(filters: GetListingsRequest): Promise<PaginatedResult<ListingItem>>;
  update(id: string, data: Partial<CreateListingRequest>): Promise<GetListingByIdResponse | null>;
  delete(id: string): Promise<boolean>;
  findByOwnerId(ownerId: string, pagination: PaginationOptions): Promise<PaginatedResult<ListingItem>>;
}
