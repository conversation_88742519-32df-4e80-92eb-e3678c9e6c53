import { Test, TestingModule } from '@nestjs/testing';
import { CreateListingUseCase } from './create-listing.use-case';
import type { IListingRepository } from '../interfaces';
import { INJECTION_TOKENS } from '../../../../shared/constants';

describe('CreateListingUseCase', () => {
  let useCase: CreateListingUseCase;
  let repository: jest.Mocked<IListingRepository>;

  beforeEach(async () => {
    const mockRepository: jest.Mocked<IListingRepository> = {
      create: jest.fn(),
      findById: jest.fn(),
      findMany: jest.fn(),
      update: jest.fn(),
      delete: jest.fn(),
      findByOwnerId: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        CreateListingUseCase,
        {
          provide: INJECTION_TOKENS.LISTING_REPOSITORY,
          useValue: mockRepository,
        },
      ],
    }).compile();

    useCase = module.get<CreateListingUseCase>(CreateListingUseCase);
    repository = module.get(INJECTION_TOKENS.LISTING_REPOSITORY);
  });

  it('should be defined', () => {
    expect(useCase).toBeDefined();
  });

  it('should create a listing successfully', async () => {
    const createRequest = {
      title: 'Test Listing',
      description: 'A test listing',
      price: 1000,
      currency: 'USD',
      location: {
        address: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zipCode: '12345',
      },
      propertyType: 'apartment' as const,
      bedrooms: 2,
      bathrooms: 1,
      amenities: [],
      images: [],
      ownerId: 'owner123',
      contactInfo: {
        email: '<EMAIL>',
      },
    };

    const mockResponse = {
      id: '1',
      title: 'Test Listing',
      price: 1000,
      status: 'active' as const,
      createdAt: new Date(),
    };

    repository.create.mockResolvedValue(mockResponse);

    const result = await useCase.execute(createRequest);

    expect(result.success).toBe(true);
    if (result.success) {
      expect(result.data.id).toBe('1');
      expect(result.data.title).toBe('Test Listing');
      expect(result.data.price).toBe(1000);
    }
    expect(repository.create).toHaveBeenCalledWith(createRequest);
  });

  it('should reject listings with invalid price', async () => {
    const createRequest = {
      title: 'Test Listing',
      description: 'A test listing',
      price: -100, // Invalid price
      currency: 'USD',
      location: {
        address: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zipCode: '12345',
      },
      propertyType: 'apartment' as const,
      bedrooms: 2,
      bathrooms: 1,
      amenities: [],
      images: [],
      ownerId: 'owner123',
      contactInfo: {
        email: '<EMAIL>',
      },
    };

    const result = await useCase.execute(createRequest);

    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.message).toBe('Price must be greater than 0');
    }
    expect(repository.create).not.toHaveBeenCalled();
  });

  it('should reject listings with negative bedrooms', async () => {
    const createRequest = {
      title: 'Test Listing',
      description: 'A test listing',
      price: 1000,
      currency: 'USD',
      location: {
        address: '123 Test St',
        city: 'Test City',
        state: 'TS',
        zipCode: '12345',
      },
      propertyType: 'apartment' as const,
      bedrooms: -1, // Invalid bedrooms
      bathrooms: 1,
      amenities: [],
      images: [],
      ownerId: 'owner123',
      contactInfo: {
        email: '<EMAIL>',
      },
    };

    const result = await useCase.execute(createRequest);

    expect(result.success).toBe(false);
    if (!result.success) {
      expect(result.error.message).toBe('Bedrooms and bathrooms must be non-negative');
    }
    expect(repository.create).not.toHaveBeenCalled();
  });
});
