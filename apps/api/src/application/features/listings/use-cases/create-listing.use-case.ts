import { Inject, Injectable } from '@nestjs/common';
import { INJECTION_TOKENS } from '../../../../shared/constants';
import { Result, UseCase } from '../../../../shared/types';
import { CreateListingRequest, CreateListingResponse } from '../dtos';
import type { IListingRepository } from '../interfaces';

@Injectable()
export class CreateListingUseCase
  implements UseCase<CreateListingRequest, CreateListingResponse>
{
  constructor(
    @Inject(INJECTION_TOKENS.LISTING_REPOSITORY)
    private readonly listingRepository: IListingRepository
  ) {}

  async execute(
    request: CreateListingRequest
  ): Promise<Result<CreateListingResponse>> {
    try {
      // Validate business rules
      if (request.price <= 0) {
        return {
          success: false,
          error: new Error('Price must be greater than 0'),
        };
      }

      if (request.bedrooms < 0 || request.bathrooms < 0) {
        return {
          success: false,
          error: new Error('Bedrooms and bathrooms must be non-negative'),
        };
      }

      // Create the listing
      const listing = await this.listingRepository.create(request);

      return {
        success: true,
        data: listing,
      };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error
            ? error
            : new Error('Failed to create listing'),
      };
    }
  }
}
