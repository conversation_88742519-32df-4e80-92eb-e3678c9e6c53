import { Inject, Injectable } from '@nestjs/common';
import { INJECTION_TOKENS } from '../../../../shared/constants';
import { Result, UseCase } from '../../../../shared/types';
import { GetListingByIdRequest, GetListingByIdResponse } from '../dtos';
import type { IListingRepository } from '../interfaces';

@Injectable()
export class GetListingByIdUseCase
  implements UseCase<GetListingByIdRequest, GetListingByIdResponse>
{
  constructor(
    @Inject(INJECTION_TOKENS.LISTING_REPOSITORY)
    private readonly listingRepository: IListingRepository
  ) {}

  async execute(
    request: GetListingByIdRequest
  ): Promise<Result<GetListingByIdResponse>> {
    try {
      if (!request.id || request.id.trim() === '') {
        return {
          success: false,
          error: new Error('Listing ID is required'),
        };
      }

      const listing = await this.listingRepository.findById(request.id);

      if (!listing) {
        return {
          success: false,
          error: new Error('Listing not found'),
        };
      }

      return {
        success: true,
        data: listing,
      };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error ? error : new Error('Failed to get listing'),
      };
    }
  }
}
