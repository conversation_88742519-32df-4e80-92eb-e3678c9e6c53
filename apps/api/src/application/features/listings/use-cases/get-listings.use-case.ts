import { Inject, Injectable } from '@nestjs/common';
import { INJECTION_TOKENS } from '../../../../shared/constants';
import { Result, UseCase } from '../../../../shared/types';
import { GetListingsRequest, GetListingsResponse } from '../dtos';
import type { IListingRepository } from '../interfaces';

@Injectable()
export class GetListingsUseCase
  implements UseCase<GetListingsRequest, GetListingsResponse>
{
  constructor(
    @Inject(INJECTION_TOKENS.LISTING_REPOSITORY)
    private readonly listingRepository: IListingRepository
  ) {}

  async execute(
    request: GetListingsRequest
  ): Promise<Result<GetListingsResponse>> {
    try {
      // Validate pagination parameters
      if (request.page < 1) {
        return {
          success: false,
          error: new Error('Page must be greater than 0'),
        };
      }

      if (request.limit < 1 || request.limit > 100) {
        return {
          success: false,
          error: new Error('Limit must be between 1 and 100'),
        };
      }

      // Validate price range
      if (
        request.minPrice &&
        request.maxPrice &&
        request.minPrice > request.maxPrice
      ) {
        return {
          success: false,
          error: new Error(
            'Minimum price cannot be greater than maximum price'
          ),
        };
      }

      // Get listings
      const result = await this.listingRepository.findMany(request);

      return {
        success: true,
        data: result,
      };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error ? error : new Error('Failed to get listings'),
      };
    }
  }
}
