import { z } from 'zod';

export const GetListingByIdRequestSchema = z.object({
  id: z.string().min(1, 'Listing ID is required'),
});

export const GetListingByIdResponseSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  price: z.number(),
  currency: z.string(),
  location: z.object({
    address: z.string(),
    city: z.string(),
    state: z.string(),
    zipCode: z.string(),
    coordinates: z.object({
      lat: z.number(),
      lng: z.number(),
    }).optional(),
  }),
  propertyType: z.enum(['apartment', 'house', 'condo', 'townhouse', 'other']),
  bedrooms: z.number(),
  bathrooms: z.number(),
  squareFootage: z.number().optional(),
  amenities: z.array(z.string()),
  images: z.array(z.string()),
  status: z.enum(['active', 'pending', 'sold', 'inactive']),
  ownerId: z.string(),
  contactInfo: z.object({
    email: z.string(),
    phone: z.string().optional(),
  }),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export type GetListingByIdRequest = z.infer<typeof GetListingByIdRequestSchema>;
export type GetListingByIdResponse = z.infer<typeof GetListingByIdResponseSchema>;
