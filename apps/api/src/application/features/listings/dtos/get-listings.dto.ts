import { z } from 'zod';

export const GetListingsRequestSchema = z.object({
  page: z.number().int().min(1).default(1),
  limit: z.number().int().min(1).max(100).default(10),
  minPrice: z.number().positive().optional(),
  maxPrice: z.number().positive().optional(),
  propertyType: z.enum(['apartment', 'house', 'condo', 'townhouse', 'other']).optional(),
  minBedrooms: z.number().int().min(0).optional(),
  maxBedrooms: z.number().int().min(0).optional(),
  minBathrooms: z.number().min(0).optional(),
  maxBathrooms: z.number().min(0).optional(),
  city: z.string().optional(),
  state: z.string().optional(),
  status: z.enum(['active', 'pending', 'sold', 'inactive']).default('active'),
  amenities: z.array(z.string()).optional(),
});

const ListingItemSchema = z.object({
  id: z.string(),
  title: z.string(),
  description: z.string(),
  price: z.number(),
  currency: z.string(),
  location: z.object({
    address: z.string(),
    city: z.string(),
    state: z.string(),
    zipCode: z.string(),
    coordinates: z.object({
      lat: z.number(),
      lng: z.number(),
    }).optional(),
  }),
  propertyType: z.enum(['apartment', 'house', 'condo', 'townhouse', 'other']),
  bedrooms: z.number(),
  bathrooms: z.number(),
  squareFootage: z.number().optional(),
  amenities: z.array(z.string()),
  images: z.array(z.string()),
  status: z.enum(['active', 'pending', 'sold', 'inactive']),
  ownerId: z.string(),
  contactInfo: z.object({
    email: z.string(),
    phone: z.string().optional(),
  }),
  createdAt: z.date(),
  updatedAt: z.date(),
});

export const GetListingsResponseSchema = z.object({
  data: z.array(ListingItemSchema),
  total: z.number(),
  page: z.number(),
  limit: z.number(),
  totalPages: z.number(),
});

export type GetListingsRequest = z.infer<typeof GetListingsRequestSchema>;
export type GetListingsResponse = z.infer<typeof GetListingsResponseSchema>;
export type ListingItem = z.infer<typeof ListingItemSchema>;
