import { z } from 'zod';

const LocationSchema = z.object({
  address: z.string().min(1, 'Address is required'),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(2, 'State is required'),
  zipCode: z.string().min(5, 'Valid zip code is required'),
  coordinates: z.object({
    lat: z.number().min(-90).max(90),
    lng: z.number().min(-180).max(180),
  }).optional(),
});

const ContactInfoSchema = z.object({
  email: z.string().email('Valid email is required'),
  phone: z.string().optional(),
});

export const CreateListingRequestSchema = z.object({
  title: z.string().min(1, 'Title is required').max(200, 'Title too long'),
  description: z.string().min(1, 'Description is required').max(2000, 'Description too long'),
  price: z.number().positive('Price must be positive'),
  currency: z.string().default('USD'),
  location: LocationSchema,
  propertyType: z.enum(['apartment', 'house', 'condo', 'townhouse', 'other']),
  bedrooms: z.number().int().min(0, 'Bedrooms must be non-negative'),
  bathrooms: z.number().min(0, 'Bathrooms must be non-negative'),
  squareFootage: z.number().positive().optional(),
  amenities: z.array(z.string()).default([]),
  images: z.array(z.string().url()).default([]),
  ownerId: z.string().min(1, 'Owner ID is required'),
  contactInfo: ContactInfoSchema,
});

export const CreateListingResponseSchema = z.object({
  id: z.string(),
  title: z.string(),
  price: z.number(),
  status: z.enum(['active', 'pending', 'sold', 'inactive']),
  createdAt: z.date(),
});

export type CreateListingRequest = z.infer<typeof CreateListingRequestSchema>;
export type CreateListingResponse = z.infer<typeof CreateListingResponseSchema>;
