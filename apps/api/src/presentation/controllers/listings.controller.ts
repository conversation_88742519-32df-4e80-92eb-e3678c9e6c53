import { 
  <PERSON>, 
  Get, 
  Post, 
  Body, 
  Param, 
  Query, 
  HttpStatus, 
  HttpException 
} from '@nestjs/common';
import { 
  CreateListingUseCase,
  GetListingsUseCase,
  GetListingByIdUseCase,
  CreateListingRequestSchema,
  GetListingsRequestSchema,
  GetListingByIdRequestSchema
} from '../../application/features/listings';
import { ZodValidationPipe } from '../validators';

@Controller('listings')
export class ListingsController {
  constructor(
    private readonly createListingUseCase: CreateListingUseCase,
    private readonly getListingsUseCase: GetListingsUseCase,
    private readonly getListingByIdUseCase: GetListingByIdUseCase,
  ) {}

  @Post()
  async createListing(@Body(new ZodValidationPipe(CreateListingRequestSchema)) body: any) {
    const result = await this.createListingUseCase.execute(body);

    if (!result.success) {
      throw new HttpException(
        {
          message: result.error.message,
          statusCode: HttpStatus.BAD_REQUEST,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    return {
      statusCode: HttpStatus.CREATED,
      data: result.data,
    };
  }

  @Get()
  async getListings(@Query(new ZodValidationPipe(GetListingsRequestSchema)) query: any) {
    const result = await this.getListingsUseCase.execute(query);

    if (!result.success) {
      throw new HttpException(
        {
          message: result.error.message,
          statusCode: HttpStatus.BAD_REQUEST,
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    return {
      statusCode: HttpStatus.OK,
      data: result.data,
    };
  }

  @Get(':id')
  async getListingById(@Param(new ZodValidationPipe(GetListingByIdRequestSchema)) params: any) {
    const result = await this.getListingByIdUseCase.execute(params);

    if (!result.success) {
      const statusCode = result.error.message === 'Listing not found' 
        ? HttpStatus.NOT_FOUND 
        : HttpStatus.BAD_REQUEST;

      throw new HttpException(
        {
          message: result.error.message,
          statusCode,
        },
        statusCode,
      );
    }

    return {
      statusCode: HttpStatus.OK,
      data: result.data,
    };
  }
}
