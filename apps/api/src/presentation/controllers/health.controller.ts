import { Controller, Get, Query, HttpStatus, HttpException } from '@nestjs/common';
import { HealthCheckUseCase } from '../../application/features/health';
import { HealthCheckRequestSchema } from '../../application/features/health/dtos';
import { ZodValidationPipe } from '../validators';

@Controller('health')
export class HealthController {
  constructor(private readonly healthCheckUseCase: HealthCheckUseCase) {}

  @Get()
  async checkHealth(@Query(new ZodValidationPipe(HealthCheckRequestSchema)) query: any) {
    const result = await this.healthCheckUseCase.execute(query);

    if (!result.success) {
      throw new HttpException(
        {
          message: result.error.message,
          statusCode: HttpStatus.INTERNAL_SERVER_ERROR,
        },
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }

    return {
      statusCode: HttpStatus.OK,
      data: result.data,
    };
  }
}
