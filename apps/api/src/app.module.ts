import { Module } from '@nestjs/common';
import { HealthCheckUseCase } from './application/features/health';
import {
  CreateListingUseCase,
  GetListingByIdUseCase,
  GetListingsUseCase,
} from './application/features/listings';
import { DatabaseProvider } from './infrastructure/providers';
import { InMemoryListingRepository } from './infrastructure/repositories/in-memory-listing.repository';
import { HealthService } from './infrastructure/services/health.service';
import {
  HealthController,
  ListingsController,
} from './presentation/controllers';
import { INJECTION_TOKENS } from './shared/constants';

@Module({
  imports: [],
  controllers: [HealthController, ListingsController],
  providers: [
    // Use Cases
    CreateListingUseCase,
    GetListingsUseCase,
    GetListingByIdUseCase,
    HealthCheckUseCase,

    // Infrastructure Providers
    DatabaseProvider,

    // Services
    {
      provide: INJECTION_TOKENS.HEALTH_SERVICE,
      useClass: HealthService,
    },

    // Repositories
    {
      provide: INJECTION_TOKENS.LISTING_REPOSITORY,
      useClass: InMemoryListingRepository,
    },
  ],
})
export class AppModule {}
