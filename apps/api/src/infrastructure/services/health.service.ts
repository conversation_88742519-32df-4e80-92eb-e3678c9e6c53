import { Injectable } from '@nestjs/common';
import { IHealthService } from '../../application/features/health';

@Injectable()
export class HealthService implements IHealthService {
  private startTime = Date.now();

  async checkHealth(): Promise<{
    status: 'healthy' | 'unhealthy';
    uptime: number;
    timestamp: Date;
    details?: Record<string, any>;
  }> {
    const uptime = Date.now() - this.startTime;
    const memoryUsage = process.memoryUsage();

    return {
      status: 'healthy',
      uptime,
      timestamp: new Date(),
      details: {
        memory: {
          rss: `${Math.round(memoryUsage.rss / 1024 / 1024)} MB`,
          heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)} MB`,
          heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)} MB`,
          external: `${Math.round(memoryUsage.external / 1024 / 1024)} MB`,
        },
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
      },
    };
  }
}
