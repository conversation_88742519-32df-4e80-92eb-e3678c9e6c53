import { Inject, Injectable } from '@nestjs/common';
import { type Database, testConnection } from '@repo/db';
import { IHealthService } from '../../application/features/health';
import { INJECTION_TOKENS } from '../../shared/constants';

@Injectable()
export class HealthService implements IHealthService {
  private startTime = Date.now();

  constructor(
    @Inject(INJECTION_TOKENS.DATABASE)
    private readonly db: Database
  ) {}

  async checkHealth(): Promise<{
    status: 'healthy' | 'unhealthy';
    uptime: number;
    timestamp: Date;
    details?: Record<string, any>;
  }> {
    const uptime = Date.now() - this.startTime;
    const memoryUsage = process.memoryUsage();

    // Test database connection
    const dbHealthy = await testConnection();

    return {
      status: dbHealthy ? 'healthy' : 'unhealthy',
      uptime,
      timestamp: new Date(),
      details: {
        database: {
          connected: dbHealthy,
        },
        memory: {
          rss: `${Math.round(memoryUsage.rss / 1024 / 1024)} MB`,
          heapTotal: `${Math.round(memoryUsage.heapTotal / 1024 / 1024)} MB`,
          heapUsed: `${Math.round(memoryUsage.heapUsed / 1024 / 1024)} MB`,
          external: `${Math.round(memoryUsage.external / 1024 / 1024)} MB`,
        },
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
      },
    };
  }
}
