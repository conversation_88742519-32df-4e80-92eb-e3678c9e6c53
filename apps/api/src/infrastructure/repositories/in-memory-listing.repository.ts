import { Injectable } from '@nestjs/common';
import {
  CreateListingRequest,
  CreateListingResponse,
  GetListingByIdResponse,
  GetListingsRequest,
  IListingRepository,
  ListingItem,
} from '../../application/features/listings';
import { PaginatedResult, PaginationOptions } from '../../shared/types';

@Injectable()
export class InMemoryListingRepository implements IListingRepository {
  private listings: Map<string, ListingItem> = new Map();
  private idCounter = 1;

  async create(data: CreateListingRequest): Promise<CreateListingResponse> {
    const id = this.idCounter.toString();
    this.idCounter++;

    const now = new Date();
    const listing: ListingItem = {
      id,
      ...data,
      status: 'active',
      createdAt: now,
      updatedAt: now,
    };

    this.listings.set(id, listing);

    return {
      id: listing.id,
      title: listing.title,
      price: listing.price,
      status: listing.status,
      createdAt: listing.createdAt,
    };
  }

  async findById(id: string): Promise<GetListingByIdResponse | null> {
    const listing = this.listings.get(id);
    return listing || null;
  }

  async findMany(
    filters: GetListingsRequest
  ): Promise<PaginatedResult<ListingItem>> {
    let filteredListings = Array.from(this.listings.values());

    // Apply filters
    if (filters.status) {
      filteredListings = filteredListings.filter(
        (l) => l.status === filters.status
      );
    }
    if (filters.propertyType) {
      filteredListings = filteredListings.filter(
        (l) => l.propertyType === filters.propertyType
      );
    }
    if (filters.minPrice) {
      filteredListings = filteredListings.filter(
        (l) => l.price >= filters.minPrice!
      );
    }
    if (filters.maxPrice) {
      filteredListings = filteredListings.filter(
        (l) => l.price <= filters.maxPrice!
      );
    }
    if (filters.minBedrooms) {
      filteredListings = filteredListings.filter(
        (l) => l.bedrooms >= filters.minBedrooms!
      );
    }
    if (filters.maxBedrooms) {
      filteredListings = filteredListings.filter(
        (l) => l.bedrooms <= filters.maxBedrooms!
      );
    }
    if (filters.city) {
      filteredListings = filteredListings.filter((l) =>
        l.location.city.toLowerCase().includes(filters.city!.toLowerCase())
      );
    }
    if (filters.state) {
      filteredListings = filteredListings.filter((l) =>
        l.location.state.toLowerCase().includes(filters.state!.toLowerCase())
      );
    }

    // Pagination
    const total = filteredListings.length;
    const totalPages = Math.ceil(total / filters.limit);
    const startIndex = (filters.page - 1) * filters.limit;
    const endIndex = startIndex + filters.limit;
    const data = filteredListings.slice(startIndex, endIndex);

    return {
      data,
      total,
      page: filters.page,
      limit: filters.limit,
      totalPages,
    };
  }

  async update(
    id: string,
    data: Partial<CreateListingRequest>
  ): Promise<GetListingByIdResponse | null> {
    const existing = this.listings.get(id);
    if (!existing) {
      return null;
    }

    const updated: ListingItem = {
      ...existing,
      ...data,
      updatedAt: new Date(),
    };

    this.listings.set(id, updated);
    return updated;
  }

  async delete(id: string): Promise<boolean> {
    return this.listings.delete(id);
  }

  async findByOwnerId(
    ownerId: string,
    pagination: PaginationOptions
  ): Promise<PaginatedResult<ListingItem>> {
    const ownerListings = Array.from(this.listings.values()).filter(
      (l) => l.ownerId === ownerId
    );

    const total = ownerListings.length;
    const totalPages = Math.ceil(total / pagination.limit);
    const startIndex = (pagination.page - 1) * pagination.limit;
    const endIndex = startIndex + pagination.limit;
    const data = ownerListings.slice(startIndex, endIndex);

    return {
      data,
      total,
      page: pagination.page,
      limit: pagination.limit,
      totalPages,
    };
  }
}
