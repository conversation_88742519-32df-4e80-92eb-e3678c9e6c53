import { Inject, Injectable } from '@nestjs/common';
import { type Database, listings, locations, properties } from '@repo/db';
import { and, eq, gte, ilike, lte } from 'drizzle-orm';
import {
  CreateListingRequest,
  CreateListingResponse,
  GetListingByIdResponse,
  GetListingsRequest,
  IListingRepository,
  ListingItem,
} from '../../application/features/listings';
import { INJECTION_TOKENS } from '../../shared/constants';
import { PaginatedResult, PaginationOptions } from '../../shared/types';

/**
 * Drizzle-based implementation of the listing repository
 *
 * This repository uses the database provider to interact with PostgreSQL
 * through Drizzle ORM, following Clean Architecture principles.
 */
@Injectable()
export class DrizzleListingRepository implements IListingRepository {
  constructor(
    @Inject(INJECTION_TOKENS.DATABASE)
    private readonly db: Database
  ) {}

  async create(data: CreateListingRequest): Promise<CreateListingResponse> {
    // For now, return a simple implementation
    // In a real implementation, you would:
    // 1. Create location record
    // 2. Create property record
    // 3. Create listing record
    // 4. Handle the complex relationships

    throw new Error('DrizzleListingRepository.create not yet implemented');
  }

  async findById(id: string): Promise<GetListingByIdResponse | null> {
    // For now, return null
    // In a real implementation, you would:
    // 1. Query listings with joins to properties and locations
    // 2. Map database results to DTO format
    // 3. Handle the property type enum mapping

    return null;
  }

  async findMany(
    filters: GetListingsRequest
  ): Promise<PaginatedResult<ListingItem>> {
    // Pagination
    const page = filters.page;
    const limit = filters.limit;
    const offset = (page - 1) * limit;

    // Helper mappers
    const statusMap: Record<string, string> = {
      active: 'ACTIVE',
      pending: 'PENDING',
      sold: 'SOLD',
      inactive: 'DRAFT',
    };
    const toApiStatus = (dbStatus: string): ListingItem['status'] => {
      switch (dbStatus) {
        case 'ACTIVE':
          return 'active';
        case 'PENDING':
          return 'pending';
        case 'SOLD':
          return 'sold';
        default:
          return 'inactive';
      }
    };
    const toDbPropertyType = (pt?: GetListingsRequest['propertyType']) => {
      if (!pt || pt === 'other') return;
      return pt.toUpperCase();
    };
    const toApiPropertyType = (dbType: string): ListingItem['propertyType'] => {
      const v = dbType.toLowerCase();
      if (
        v === 'apartment' ||
        v === 'house' ||
        v === 'condo' ||
        v === 'townhouse'
      )
        return v as any;
      return 'other';
    };

    // Build where conditions (using casts to avoid Drizzle type duplication issues across packages)
    const where: any[] = [];
    if (filters.status) {
      const s = statusMap[filters.status];
      if (s) where.push((eq as any)(listings.status, s));
    }
    if (filters.minPrice != null)
      where.push((gte as any)(listings.price, filters.minPrice));
    if (filters.maxPrice != null)
      where.push((lte as any)(listings.price, filters.maxPrice));
    const dbPropType = toDbPropertyType(filters.propertyType);
    if (dbPropType)
      where.push((eq as any)(properties.propertyType, dbPropType));
    if (filters.minBedrooms != null)
      where.push((gte as any)(properties.bedrooms, filters.minBedrooms));
    if (filters.maxBedrooms != null)
      where.push((lte as any)(properties.bedrooms, filters.maxBedrooms));
    if (filters.minBathrooms != null)
      where.push((gte as any)(properties.bathrooms, filters.minBathrooms));
    if (filters.maxBathrooms != null)
      where.push((lte as any)(properties.bathrooms, filters.maxBathrooms));
    if (filters.city)
      where.push((ilike as any)(locations.city, `%${filters.city}%`));
    if (filters.state)
      where.push((ilike as any)(locations.state, `%${filters.state}%`));

    // Base query with joins
    let query: any = (this.db as any)
      .select({
        id: listings.id,
        title: listings.title,
        description: listings.description,
        price: listings.price,
        currency: listings.currency,
        address: listings.address,
        status: listings.status,
        createdAt: listings.createdAt,
        updatedAt: listings.updatedAt,
        bedrooms: properties.bedrooms,
        bathrooms: properties.bathrooms,
        propertyType: properties.propertyType,
        city: locations.city,
        state: locations.state,
        postalCode: locations.postalCode,
        latitude: locations.latitude,
        longitude: locations.longitude,
      })
      .from(listings)
      .innerJoin(properties, (eq as any)(properties.id, listings.propertyId))
      .innerJoin(locations, (eq as any)(locations.id, properties.locationId));

    if (where.length) {
      query = query.where((and as any)(...where));
    }

    // Count total
    let countQuery: any = (this.db as any)
      .select({ count: listings.id as any }) // placeholder to build from()
      .from(listings)
      .innerJoin(properties, (eq as any)(properties.id, listings.propertyId))
      .innerJoin(locations, (eq as any)(locations.id, properties.locationId));
    if (where.length) {
      countQuery = countQuery.where((and as any)(...where));
    }

    // Execute queries
    const [rows, countRows] = await Promise.all([
      query.limit(limit).offset(offset),
      countQuery,
    ]);

    const total = Array.isArray(countRows) ? countRows.length : 0; // fallback if count aggregation not set

    const data: ListingItem[] = rows.map((r: any) => ({
      id: r.id,
      title: r.title,
      description: r.description,
      price: r.price,
      currency: r.currency,
      location: {
        address: r.address,
        city: r.city ?? '',
        state: r.state ?? '',
        zipCode: r.postalCode ?? '',
        coordinates:
          r.latitude != null && r.longitude != null
            ? { lat: r.latitude, lng: r.longitude }
            : undefined,
      },
      propertyType: toApiPropertyType(r.propertyType),
      bedrooms: r.bedrooms,
      bathrooms: r.bathrooms,
      squareFootage: undefined,
      amenities: [],
      images: [],
      status: toApiStatus(r.status),
      ownerId: '',
      contactInfo: { email: '' },
      createdAt: r.createdAt,
      updatedAt: r.updatedAt,
    }));

    const totalPages = Math.ceil(total / limit);

    return {
      data,
      total,
      page,
      limit,
      totalPages,
    };
  }

  async update(
    id: string,
    data: Partial<CreateListingRequest>
  ): Promise<GetListingByIdResponse | null> {
    // For now, return null
    // In a real implementation, you would:
    // 1. Update the listing record
    // 2. Update related property/location records if needed
    // 3. Return the updated listing

    return null;
  }

  async delete(_id: string): Promise<boolean> {
    // For now, return false
    // In a real implementation, you would:
    // 1. Delete the listing record using Drizzle
    // 2. Handle cascading deletes if needed
    // 3. Return success/failure status

    return false;
  }

  async findByOwnerId(
    _ownerId: string,
    pagination: PaginationOptions
  ): Promise<PaginatedResult<ListingItem>> {
    // For now, return empty results
    // In a real implementation, you would:
    // 1. Query listings by owner ID
    // 2. Apply pagination
    // 3. Join with related tables
    // 4. Map to ListingItem format

    return {
      data: [],
      total: 0,
      page: pagination.page,
      limit: pagination.limit,
      totalPages: 0,
    };
  }

  /**
   * Test method to verify database connection
   */
  async testConnection(): Promise<boolean> {
    try {
      // Simple test - just check if db is available
      // In a real implementation, you would run a simple query
      return this.db !== null;
    } catch {
      return false;
    }
  }
}
