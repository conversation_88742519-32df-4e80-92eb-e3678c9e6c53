import { Provider } from '@nestjs/common';
import { db } from '@repo/db';
import { INJECTION_TOKENS } from '../../shared/constants';

/**
 * Database provider for dependency injection
 *
 * This provider makes the Drizzle database client available throughout the application
 * following Clean Architecture principles by keeping database concerns in the infrastructure layer.
 */
export const DatabaseProvider: Provider = {
  provide: INJECTION_TOKENS.DATABASE,
  useValue: db,
};

// Re-export the Database type from the db package
export type { Database } from '@repo/db';
