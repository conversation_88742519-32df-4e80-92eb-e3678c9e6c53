# Clean Architecture Implementation

This NestJS application follows Clean Architecture principles with a feature-based organization and uses Fastify as the HTTP adapter.

## Architecture Overview

The application is organized into the following layers:

```
src/
├── application/           # Application Layer (Use Cases, DTOs, Interfaces)
│   └── features/         # Feature-based organization
│       ├── listings/     # Listings feature
│       └── health/       # Health check feature
├── infrastructure/       # Infrastructure Layer (Repositories, Services, Adapters)
│   ├── repositories/     # Data access implementations
│   ├── services/         # External service implementations
│   └── adapters/         # External adapters
├── presentation/         # Presentation Layer (Controllers, Middleware, Validation)
│   ├── controllers/      # HTTP controllers
│   ├── middleware/       # Custom middleware
│   └── validators/       # Validation pipes and decorators
└── shared/              # Shared utilities and types
    ├── constants/       # Application constants and injection tokens
    ├── types/           # Common types and interfaces
    └── utils/           # Utility functions
```

## Key Principles

### 1. Dependency Direction
Dependencies point inward toward the application core:
- Presentation → Application
- Infrastructure → Application
- Application has no dependencies on outer layers

### 2. Feature-Based Organization
The application layer is organized by features rather than technical concerns:
- Each feature contains its own use cases, DTOs, and interfaces
- Features are self-contained and can be developed independently

### 3. Interface Segregation
- Interfaces are defined in the application layer
- Infrastructure implementations depend on these interfaces
- Dependency injection is used to wire implementations

## Technology Stack

- **Framework**: NestJS
- **HTTP Adapter**: Fastify (migrated from Express)
- **Validation**: Zod schemas with custom validation pipes
- **Testing**: Jest with custom test utilities
- **Architecture**: Clean Architecture with CQRS patterns

## Features

### Health Check Feature
- **Endpoint**: `GET /api/health`
- **Use Case**: `HealthCheckUseCase`
- **Repository**: `HealthService`
- **Validation**: Zod schema validation

### Listings Feature
- **Endpoints**: 
  - `GET /api/listings` - Get paginated listings with filters
  - `GET /api/listings/:id` - Get listing by ID
  - `POST /api/listings` - Create new listing
- **Use Cases**: 
  - `GetListingsUseCase`
  - `GetListingByIdUseCase`
  - `CreateListingUseCase`
- **Repository**: `InMemoryListingRepository` (can be replaced with database implementation)
- **Validation**: Comprehensive Zod schemas for all operations

## Running the Application

```bash
# Development
pnpm run start:dev

# Production build
pnpm run build
pnpm run start:prod

# Tests
pnpm test              # Unit tests
pnpm run test:e2e      # End-to-end tests
pnpm run test:cov      # Coverage report
```

## API Examples

### Health Check
```bash
curl -X GET "http://localhost:3001/api/health"
curl -X GET "http://localhost:3001/api/health?includeDetails=true"
```

### Listings
```bash
# Get all listings
curl -X GET "http://localhost:3001/api/listings"

# Get listings with filters
curl -X GET "http://localhost:3001/api/listings?minPrice=1000&maxPrice=5000&propertyType=apartment"

# Create a listing
curl -X POST "http://localhost:3001/api/listings" \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Beautiful 2BR Apartment",
    "description": "A lovely apartment in downtown",
    "price": 2500,
    "location": {
      "address": "123 Main St",
      "city": "San Francisco",
      "state": "CA",
      "zipCode": "94102"
    },
    "propertyType": "apartment",
    "bedrooms": 2,
    "bathrooms": 2,
    "ownerId": "owner123",
    "contactInfo": {
      "email": "<EMAIL>"
    }
  }'

# Get listing by ID
curl -X GET "http://localhost:3001/api/listings/1"
```

## Testing Strategy

- **Unit Tests**: Test use cases in isolation with mocked dependencies
- **Integration Tests**: Test controllers with real use cases but mocked repositories
- **E2E Tests**: Test complete request/response cycle with Fastify adapter

## Migration from Express to Fastify

The application has been successfully migrated from Express to Fastify:
- Updated `main.ts` to use `FastifyAdapter`
- Configured CORS and global prefix
- Updated tests to work with Fastify
- Maintained compatibility with existing NestJS features

## Adding New Features

To add a new feature:

1. Create feature directory in `src/application/features/`
2. Define DTOs with Zod schemas
3. Create interfaces for external dependencies
4. Implement use cases
5. Add repository/service implementations in infrastructure
6. Create controllers in presentation layer
7. Wire everything together in `app.module.ts`
8. Add tests for all layers
