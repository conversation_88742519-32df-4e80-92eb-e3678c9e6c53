import { config } from 'dotenv';
import { drizzle } from 'drizzle-orm/node-postgres';
import { Pool } from 'pg';
import schema from './schema/index.js';

// Load environment variables
config();

/**
 * Database connection configuration
 */
const connectionConfig = {
  connectionString: process.env.DATABASE_URL,
  // Connection pool settings
  max: 20, // Maximum number of clients in the pool
  idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
  connectionTimeoutMillis: 2000, // Return an error after 2 seconds if connection could not be established
};

/**
 * PostgreSQL connection pool
 */
export const pool = new Pool(connectionConfig);

/**
 * Drizzle database client with schema
 */
export const db = drizzle(pool, { schema });

/**
 * Database client type for dependency injection
 */
export type Database = typeof db;

/**
 * Close database connections (useful for testing and graceful shutdown)
 */
export const closeDatabase = async (): Promise<void> => {
  await pool.end();
};

/**
 * Test database connection
 */
export const testConnection = async (): Promise<boolean> => {
  try {
    const client = await pool.connect();
    await client.query('SELECT 1');
    client.release();
    return true;
  } catch (error) {
    console.error('Database connection test failed:', error);
    return false;
  }
};
